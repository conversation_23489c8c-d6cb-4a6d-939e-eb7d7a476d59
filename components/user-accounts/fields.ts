import type { Page } from "@/types";
import type { Header } from "vue3-easy-data-table";

export const useAccountsFields = () => {
  const HEADERS = ref<Header[]>([
    { text: "id", value: "id", sortable: true },
    { text: "username", value: "username", sortable: true },
    { text: "full name", value: "fullname", sortable: true },
    { text: "sex", value: "sex", sortable: true },
    { text: "actions", value: "actions" },
  ]);
  const PAGES = ref<Page>([
    {
      name: "Home",
      link: "/home",
    },
    {
      name: "Access Controls",
      link: "#",
    },
  ]);
  return {
    HEADERS,
    PAGES
  };
};
